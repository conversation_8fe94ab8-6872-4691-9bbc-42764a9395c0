"""
Model architectures for DataYatesV1.

This module contains the main model classes including single-dataset and multi-dataset variants.
"""

import torch
import torch.nn as nn
from typing import Dict, Any, List, Union
from .norm_act_pool import get_activation_layer

# Type aliases for clarity
ConfigDict = Dict[str, Any]


class ModularV1Model(nn.Module):
    """
    A modular V1 model architecture that allows easy swapping of components.

    The basic model architecture is as follows:
    stim (B, C_stim, T, H, W) ─► frontend ─► convcore ─► (B,C_conv,T,H,W) features
                                                |
                                behaviour  ─► MLP  (C_b)
                                                |
                                    concat (or FiLM) along channel dim
                                        (B,C_mod,T,H,W)
                                                ▼
                                            ConvGRU/LSTM
                                            (B,C_rec, T, H, W)
                                                ▼
                                            readout (factorised Gaussian or linear)
                                            (B, n_units)
                                                ▼
                                            activation (nn.Softplus()) ─► spikes
                                            

    This model is built from modular components that can be mixed and matched:
    - Frontend: Processes stimulus with a "front end" (da, conv, etc.) - "da" is the dynamic adaptation model from <PERSON> et al., 2013.
    - ConvNet: Feature extraction (DenseNet, CNN, ResNet, etc.)
    - Modulator: Behavioral modulation (MLP, Linear, etc.) - Optional, combines with convnet output before recurrent or readout.
    - Recurrent: Temporal processing (ConvLSTM, ConvGRU, etc.) - Optional
    - Readout: Output layer (DynamicGaussian, Linear, etc.)

    The model can be configured to skip certain components (like recurrent layers)
    by setting their type to 'none'.
    """
    def __init__(self, config: ConfigDict):
        """
        Initialize the model with the given configuration.

        Args:
            config: Dictionary containing model configuration
        """
        super().__init__()

        # Extract basic parameters
        self.height = config.get('height', None)
        self.width = config.get('width', None)
        self.sampling_rate = config.get('sampling_rate', 240)
        self.initial_input_channels = config.get('initial_input_channels', 1)

        # Set up activation function
        self.activation = config.get('output_activation', nn.Softplus())

        # Build the model components
        self._build_model(config)

    def _build_model(self, config: ConfigDict, verbose=False):
        """Build all model components based on configuration."""
        # Import factory functions to avoid circular imports
        from ..factory import create_frontend, create_convnet, create_modulator, create_recurrent, create_readout

        # Track channel dimensions between components
        current_channels = self.initial_input_channels
        if verbose:
            print(f"Initial channels: {current_channels}")

        # get all configs
        frontend_config = config.get('frontend', {'type': 'none', 'params': {}})
        convnet_config = config.get('convnet', {'type': 'densenet', 'params': {}})
        modulator_config = config.get('modulator', {'type': 'none', 'mode': 'concatenate', 'params': {}})
        recurrent_config = config.get('recurrent', {'type': 'none', 'params': {}})
        readout_config = config.get('readout', {'type': 'gaussian', 'params': {}})

        # Build frontend
        frontend_type = frontend_config['type']
        frontend_params = frontend_config['params']
        self.frontend, current_channels = create_frontend(
            frontend_type=frontend_type,
            in_channels=current_channels,
            sampling_rate=self.sampling_rate,
            **frontend_params
        )

        # Build convnet
        if verbose:
            print(f"Current channels before convnet: {current_channels}")
        convnet_type = convnet_config['type']
        convnet_params = convnet_config['params']
        self.convnet, current_channels = create_convnet(
            convnet_type=convnet_type,
            in_channels=current_channels,
            **convnet_params
        )
        if verbose:
            print(f"Current channels after convnet: {current_channels}")

        # Build modulator (needed before recurrent to know dimensions)
        modulator_type = modulator_config['type']
        modulator_params = modulator_config['params']
        modulator_params['feature_dim'] = current_channels
        self.modulator, modulator_dim = create_modulator(
            modulator_type=modulator_type,
            **modulator_params
        )

        # Build recurrent
        recurrent_type = recurrent_config['type']
        recurrent_params = recurrent_config['params']

        # update input_dims depending on recurrent type
        if recurrent_type in ['lstm', 'gru']:
            assert readout_config['type'] == 'linear', 'LSTM/GRU only compatible with linear readout. There are no spatial dimensions'
            # error out with message
            raise NotImplementedError('LSTM/GRU used, flattening conv output. This is not supported yet.')

        # update for modulator
        if self.modulator is not None and modulator_dim > 0:
            # For concat modulators, add the modulator output channels
            # For FiLM modulators, channel count stays the same
            modulator_type = modulator_config.get('type', 'none')
            if modulator_type == 'concat':
                current_channels += modulator_dim
            elif modulator_type == 'film':
                # FiLM doesn't change channel count
                pass
            else:
                raise ValueError(f"Unknown modulator type: {modulator_type}")

        # create recurrent
        if verbose:
            print(f"Current channels before recurrent: {current_channels}")
        self.recurrent, current_channels = create_recurrent(
            recurrent_type=recurrent_type,
            input_dim=current_channels,
            **recurrent_params
        )

        # Build readout
        if verbose:
            print(f"Current channels before readout: {current_channels}")
        readout_type = readout_config['type']
        readout_params = readout_config['params']
        self.readout = create_readout(
            readout_type=readout_type,
            in_channels=current_channels,
            **readout_params
        )

    def forward(self, stimulus, behavior=None):
        """
        Forward pass through the model.

        Args:
            stimulus: Visual stimulus tensor with shape (N, C, T, H, W)
            behavior: Optional behavioral data with shape (N, n_vars)

        Returns:
            Tensor: Model predictions with shape (N, n_units)
        """

        # Process through frontend
        x = self.frontend(stimulus)

        # Process through convnet
        x_conv = self.convnet(x)

        # Process through modulator
        if self.modulator is not None and behavior is not None:
            x_conv = self.modulator(x_conv, behavior)

        # Process through recurrent    
        x_recurrent = self.recurrent(x_conv)

        # Process through readout
        output = self.readout(x_recurrent)

        # Apply activation function
        output = self.activation(output)

        return output


class MultiDatasetV1Model(ModularV1Model):
    """
    Multi-dataset variant of ModularV1Model.
    
    This model supports training on multiple datasets simultaneously by having:
    - Multiple frontends (one per dataset) - all must be 'adapter' type
    - Shared convnet, modulator, and recurrent components
    - Multiple readouts (one per dataset)
    
    During training, the model routes data through the appropriate frontend/readout
    based on the dataset_idx parameter.
    """
    
    def __init__(self, model_config: ConfigDict, dataset_configs: List[ConfigDict]):
        """
        Initialize the multi-dataset model.
        
        Args:
            model_config: Main model configuration (shared components)
            dataset_configs: List of dataset-specific configurations
        """
        # Don't call super().__init__() as we need custom initialization
        nn.Module.__init__(self)
        
        # Store configurations
        self.model_config = model_config
        self.dataset_configs = dataset_configs
        self.num_datasets = len(dataset_configs)
        
        # Extract basic parameters from model config
        self.height = model_config.get('height', None)
        self.width = model_config.get('width', None)
        self.sampling_rate = model_config.get('sampling_rate', 240)
        self.initial_input_channels = model_config.get('initial_input_channels', 1)
        
        # Set up activation function
        self.activation = get_activation_layer(model_config.get('output_activation', 'none'))
        
        # Validate configurations
        self._validate_configs()
        
        # Build the model components
        self._build_multidataset_model()
    
    def _validate_configs(self):
        """Validate that dataset configs are compatible with multidataset training."""
        # Check that all datasets use adapter frontend
        for i, dataset_config in enumerate(self.dataset_configs):
            frontend_config = dataset_config.get('frontend', {})
            frontend_type = frontend_config.get('type', 'none')
            if frontend_type != 'adapter':
                raise ValueError(f"Dataset {i} uses frontend type '{frontend_type}'. "
                               "All datasets must use 'adapter' frontend for multidataset training.")
        
        # Check that all datasets have the same sampling rate
        model_sampling_rate = self.sampling_rate
        for i, dataset_config in enumerate(self.dataset_configs):
            if dataset_config.get('sampling_rate', model_sampling_rate) != model_sampling_rate:
                raise ValueError(f"Dataset {i} has different sampling rate. "
                               "All datasets must have the same sampling rate.")
    
    def _build_multidataset_model(self):
        """Build all model components for multidataset training."""
        # Import factory functions to avoid circular imports
        from ..factory import create_frontend, create_convnet, create_modulator, create_recurrent, create_readout

        # Build per-dataset frontends (all adapter type)
        self.frontends = nn.ModuleList()
        frontend_output_channels = self.initial_input_channels  # Adapter preserves channels

        for dataset_config in self.dataset_configs:
            frontend_config = dataset_config.get('frontend', {'type': 'adapter', 'params': {}})
            frontend_params = frontend_config['params']
            frontend, _ = create_frontend(
                frontend_type='adapter',
                in_channels=self.initial_input_channels,
                sampling_rate=self.sampling_rate,
                **frontend_params
            )
            self.frontends.append(frontend)
        
        # Build shared convnet
        convnet_config = self.model_config.get('convnet', {'type': 'densenet', 'params': {}})
        convnet_type = convnet_config['type']
        convnet_params = convnet_config['params']
        self.convnet, convnet_output_channels = create_convnet(
            convnet_type=convnet_type,
            in_channels=frontend_output_channels,
            **convnet_params
        )
        print(f"Convnet output channels: {convnet_output_channels}")
        
        # Build shared modulator
        modulator_config = self.model_config.get('modulator', {'type': 'none', 'params': {}})
        modulator_type = modulator_config['type']
        modulator_params = modulator_config['params'].copy()
        modulator_params['feature_dim'] = convnet_output_channels
        self.modulator, modulator_dim = create_modulator(
            modulator_type=modulator_type,
            **modulator_params
        )
        
        # Calculate channels after modulation
        current_channels = convnet_output_channels
        if self.modulator is not None and modulator_dim > 0:
            if modulator_type == 'concat':
                current_channels += modulator_dim
            elif modulator_type == 'film':
                pass  # FiLM doesn't change channel count
        
        # Build shared recurrent
        recurrent_config = self.model_config.get('recurrent', {'type': 'none', 'params': {}})
        recurrent_type = recurrent_config['type']
        recurrent_params = recurrent_config['params']
        self.recurrent, recurrent_output_channels = create_recurrent(
            recurrent_type=recurrent_type,
            input_dim=current_channels,
            **recurrent_params
        )
        
        # Build per-dataset readouts
        self.readouts = nn.ModuleList()
        for dataset_config in self.dataset_configs:
            readout_config = dataset_config.get('readout', {'type': 'gaussian', 'params': {}})
            readout_type = readout_config['type']
            readout_params = readout_config['params'].copy()
            
            # Set n_units based on dataset cids
            cids = dataset_config.get('cids', [])
            readout_params['n_units'] = len(cids)
            
            readout = create_readout(
                readout_type=readout_type,
                in_channels=recurrent_output_channels,
                **readout_params
            )
            self.readouts.append(readout)
    
    def forward(self, stimulus, dataset_idx: int, behavior=None):
        """
        Forward pass through the model for a specific dataset.
        
        Args:
            stimulus: Visual stimulus tensor with shape (N, C, T, H, W)
            dataset_idx: Index of the dataset (determines frontend/readout)
            behavior: Optional behavioral data with shape (N, n_vars)
        
        Returns:
            Tensor: Model predictions with shape (N, n_units_for_dataset)
        """
        # Route through appropriate frontend
        x = self.frontends[dataset_idx](stimulus)
        
        # Process through shared convnet
        x_conv = self.convnet(x)
        
        # Process through shared modulator
        if self.modulator is not None and behavior is not None:
            x_conv = self.modulator(x_conv, behavior)
        
        # Process through shared recurrent
        x_recurrent = self.recurrent(x_conv)
        
        # Route through appropriate readout
        output = self.readouts[dataset_idx](x_recurrent)
        
        # Apply activation function
        output = self.activation(output)
        
        return output
